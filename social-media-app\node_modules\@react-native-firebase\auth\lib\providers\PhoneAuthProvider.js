/* eslint-disable @typescript-eslint/no-unused-vars */
/*
 * Copyright (c) 2016-present Invertase Limited & Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this library except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

const providerId = 'phone';

export default class PhoneAuthProvider {
  constructor(auth) {
    if (auth === undefined) {
      throw new Error('`new PhoneAuthProvider()` is not supported on the native Firebase SDKs.');
    }
    this._auth = auth;
  }

  static get PROVIDER_ID() {
    return providerId;
  }

  static credential(verificationId, code) {
    return {
      token: verificationId,
      secret: code,
      providerId,
    };
  }

  verifyPhoneNumber(phoneInfoOptions, appVerifier) {
    if (phoneInfoOptions.multiFactorHint) {
      return this._auth.app
        .auth()
        .verifyPhoneNumberWithMultiFactorInfo(
          phoneInfoOptions.multiFactorHint,
          phoneInfoOptions.session,
        );
    }
    return this._auth.app.auth().verifyPhoneNumberForMultiFactor(phoneInfoOptions);
  }
}
