{"name": "@react-native-firebase/auth", "version": "22.2.0", "author": "Invertase <<EMAIL>> (http://invertase.io)", "description": "React Native Firebase - The authentication module provides an easy-to-use API to integrate an authentication workflow into new and existing applications. React Native Firebase provides access to all Firebase authentication methods and identity providers.", "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"build": "genversion --semi lib/version.js", "build:clean": "rimraf android/build && rimraf ios/build", "build:plugin": "rimraf plugin/build && tsc --build plugin", "lint:plugin": "eslint plugin/src/*", "prepare": "yarn run build && yarn run build:plugin"}, "repository": {"type": "git", "url": "https://github.com/invertase/react-native-firebase/tree/main/packages/auth"}, "license": "Apache-2.0", "keywords": ["react", "react-native", "firebase", "auth"], "dependencies": {"plist": "^3.1.0"}, "peerDependencies": {"@react-native-firebase/app": "22.2.0", "expo": ">=47.0.0"}, "devDependencies": {"@types/plist": "^3.0.5", "expo": "^52.0.46"}, "peerDependenciesMeta": {"expo": {"optional": true}}, "publishConfig": {"access": "public", "provenance": true}, "gitHead": "b302210509ceac8078b5fb9fd0b24e68f0641c6a"}