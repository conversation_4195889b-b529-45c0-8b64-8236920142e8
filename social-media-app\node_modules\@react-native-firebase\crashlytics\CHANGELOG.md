# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [22.2.0](https://github.com/invertase/react-native-firebase/compare/v22.1.0...v22.2.0) (2025-05-12)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [22.1.0](https://github.com/invertase/react-native-firebase/compare/v22.0.0...v22.1.0) (2025-04-30)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [22.0.0](https://github.com/invertase/react-native-firebase/compare/v21.14.0...v22.0.0) (2025-04-25)

### Bug Fixes

- **android:** use `=` assignment vs deprecated space-assignment ([39c2ecb](https://github.com/invertase/react-native-firebase/commit/39c2ecb0069a8a5a65b04fb7f86ccecf83273868))
- enable provenance signing during publish ([4535f0d](https://github.com/invertase/react-native-firebase/commit/4535f0d5756c89aeb8f8e772348c71d8176348be))

## [21.14.0](https://github.com/invertase/react-native-firebase/compare/v21.13.0...v21.14.0) (2025-04-14)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [21.13.0](https://github.com/invertase/react-native-firebase/compare/v21.12.3...v21.13.0) (2025-03-31)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [21.12.3](https://github.com/invertase/react-native-firebase/compare/v21.12.2...v21.12.3) (2025-03-26)

### Bug Fixes

- **crashlytics): Revert "fix(crashlytics:** convert internal API usage to modular" ([4f7fb2e](https://github.com/invertase/react-native-firebase/commit/4f7fb2e383a803de6963dbd01cf2c623e431d1da))
- **crashlytics:** disable deprecation warnings for analytics in crashlytics ([b600a20](https://github.com/invertase/react-native-firebase/commit/b600a2099c2076689510193b74ac29c057d27d2a))

## [21.12.2](https://github.com/invertase/react-native-firebase/compare/v21.12.1...v21.12.2) (2025-03-23)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [21.12.1](https://github.com/invertase/react-native-firebase/compare/v21.12.0...v21.12.1) (2025-03-22)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [21.12.0](https://github.com/invertase/react-native-firebase/compare/v21.11.0...v21.12.0) (2025-03-03)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [21.11.0](https://github.com/invertase/react-native-firebase/compare/v21.10.1...v21.11.0) (2025-02-20)

### Bug Fixes

- **crashlytics:** convert internal API usage to modular ([b462330](https://github.com/invertase/react-native-firebase/commit/b46233049396764b68c6108e5b886628613f2620))

## [21.10.1](https://github.com/invertase/react-native-firebase/compare/v21.10.0...v21.10.1) (2025-02-18)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [21.10.0](https://github.com/invertase/react-native-firebase/compare/v21.9.0...v21.10.0) (2025-02-11)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [21.9.0](https://github.com/invertase/react-native-firebase/compare/v21.8.0...v21.9.0) (2025-02-11)

### Bug Fixes

- firebase-ios-sdk 11.8.0 / firebase-android-sdk 33.9.0 ([67aba08](https://github.com/invertase/react-native-firebase/commit/67aba08c00aa46b72fcb1353bd428fa552b6686a))

## [21.8.0](https://github.com/invertase/react-native-firebase/compare/v21.7.4...v21.8.0) (2025-02-10)

### Bug Fixes

- **crashlytics:** internally use modular APIs during initialization ([140507f](https://github.com/invertase/react-native-firebase/commit/140507f4ddc09d004ba6f4f9c83ea1364c536c65))
- do not ship unit tests in released packages ([e71dadf](https://github.com/invertase/react-native-firebase/commit/e71dadfc1c0cad2e89c94100913af31ddf7d9c91))

## [21.7.4](https://github.com/invertase/react-native-firebase/compare/v21.7.3...v21.7.4) (2025-02-08)

### Bug Fixes

- the init calls for modular should use modular getApp() ([79da98b](https://github.com/invertase/react-native-firebase/commit/79da98bf4ecf7860db61b2813b87673f1cd0adfd))

## [21.7.3](https://github.com/invertase/react-native-firebase/compare/v21.7.2...v21.7.3) (2025-02-08)

### Bug Fixes

- use same deprecation message everywhere, correct tests for new message ([684081b](https://github.com/invertase/react-native-firebase/commit/684081b7bdc17bc314fce827972dca5b1a58e01b))

## [21.7.2](https://github.com/invertase/react-native-firebase/compare/v21.7.1...v21.7.2) (2025-02-05)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [21.7.1](https://github.com/invertase/react-native-firebase/compare/v21.7.0...v21.7.1) (2025-01-20)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [21.7.0](https://github.com/invertase/react-native-firebase/compare/v21.6.2...v21.7.0) (2025-01-16)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [21.6.2](https://github.com/invertase/react-native-firebase/compare/v21.6.1...v21.6.2) (2025-01-02)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [21.6.1](https://github.com/invertase/react-native-firebase/compare/v21.6.0...v21.6.1) (2024-11-25)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [21.6.0](https://github.com/invertase/react-native-firebase/compare/v21.5.0...v21.6.0) (2024-11-20)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [21.5.0](https://github.com/invertase/react-native-firebase/compare/v21.4.1...v21.5.0) (2024-11-16)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [21.4.1](https://github.com/invertase/react-native-firebase/compare/v21.4.0...v21.4.1) (2024-11-13)

### Reverts

- Revert "fix(ios, sdk): constrain transitive dependencies more tightly" ([1ff247c](https://github.com/invertase/react-native-firebase/commit/1ff247cd73804efbd52eb9490f68087685de814c))

## [21.4.0](https://github.com/invertase/react-native-firebase/compare/v21.3.0...v21.4.0) (2024-11-07)

### Features

- enable tvOS installation on all package with upstream support ([e8e5f7f](https://github.com/invertase/react-native-firebase/commit/e8e5f7f9aab9a58aabf9a7b0cd756584240dbc48))

## [21.3.0](https://github.com/invertase/react-native-firebase/compare/v21.2.0...v21.3.0) (2024-10-31)

### Features

- **crashlytics, ios:** add native helper log / setCustomValue methods ([da1ac6b](https://github.com/invertase/react-native-firebase/commit/da1ac6b00d4209ca431259e192c91797d3c45cb9))

## [21.2.0](https://github.com/invertase/react-native-firebase/compare/v21.1.1...v21.2.0) (2024-10-22)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [21.1.1](https://github.com/invertase/react-native-firebase/compare/v21.1.0...v21.1.1) (2024-10-22)

### Bug Fixes

- **ios, sdk:** constrain transitive dependencies more tightly ([d03ab42](https://github.com/invertase/react-native-firebase/commit/d03ab42a163a17268bac344ccd135dc18849e1be))

## [21.1.0](https://github.com/invertase/react-native-firebase/compare/v21.0.0...v21.1.0) (2024-10-21)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [21.0.0](https://github.com/invertase/react-native-firebase/compare/v20.5.0...v21.0.0) (2024-09-26)

### Bug Fixes

- **crashlytics, ios:** dependencies init param removed upstream in v11+ ([73b596c](https://github.com/invertase/react-native-firebase/commit/73b596c062d67185868de697732378beab198397))

## [20.5.0](https://github.com/invertase/react-native-firebase/compare/v20.4.0...v20.5.0) (2024-09-11)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [20.4.0](https://github.com/invertase/react-native-firebase/compare/v20.3.0...v20.4.0) (2024-08-13)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [20.3.0](https://github.com/invertase/react-native-firebase/compare/v20.2.1...v20.3.0) (2024-07-19)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [20.2.1](https://github.com/invertase/react-native-firebase/compare/v20.2.0...v20.2.1) (2024-07-17)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [20.2.0](https://github.com/invertase/react-native-firebase/compare/v20.1.0...v20.2.0) (2024-07-15)

### Bug Fixes

- **app, android:** adopt firebase-android-sdk 31.1.1 ([dba1beb](https://github.com/invertase/react-native-firebase/commit/dba1beba97d88d1110e0838b6287fd4907cfa8a7))
- **crashlytics, ios:** init w/componentsToRegister vs configureWithApp ([ca07cad](https://github.com/invertase/react-native-firebase/commit/ca07cadd592487102b035a24b55f593f065ef4a5))

## [20.1.0](https://github.com/invertase/react-native-firebase/compare/v20.0.0...v20.1.0) (2024-06-04)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [20.0.0](https://github.com/invertase/react-native-firebase/compare/v19.3.0...v20.0.0) (2024-05-20)

### ⚠ BREAKING CHANGES

- **app, android:** - requires minSdk 21+ in general, 23+ for auth module

* requires compileSdk 34+
* app-distribution gradle plugin requires
  - gradle 7.3+
  - android gradle plugin 7+
  - google services plugin 4.3.2+
* crashlytics plugin requires
  - gradle 8+
  - android gradle plugin 8.1+
  - google services plugin 4.4.1+

### Features

- **app, android:** android-sdk 33.0.0 - needs minSdk 21+ (23+ for auth) ([f29fecb](https://github.com/invertase/react-native-firebase/commit/f29fecbe72c27e60f8fec1cee6fa879b788d27b3))

## [19.3.0](https://github.com/invertase/react-native-firebase/compare/v19.2.2...v19.3.0) (2024-05-20)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [19.2.2](https://github.com/invertase/react-native-firebase/compare/v19.2.1...v19.2.2) (2024-04-13)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [19.2.1](https://github.com/invertase/react-native-firebase/compare/v19.2.0...v19.2.1) (2024-04-12)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [19.2.0](https://github.com/invertase/react-native-firebase/compare/v19.1.2...v19.2.0) (2024-04-10)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [19.1.2](https://github.com/invertase/react-native-firebase/compare/v19.1.1...v19.1.2) (2024-04-03)

### Bug Fixes

- **deps, ios:** FirebaseCoreExtension is not always versioned ([cb7ed0e](https://github.com/invertase/react-native-firebase/commit/cb7ed0eb15758a4324d2ce785513a48bfe54fe1e))

## [19.1.1](https://github.com/invertase/react-native-firebase/compare/v19.1.0...v19.1.1) (2024-03-26)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [19.1.0](https://github.com/invertase/react-native-firebase/compare/v19.0.1...v19.1.0) (2024-03-23)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [19.0.1](https://github.com/invertase/react-native-firebase/compare/v19.0.0...v19.0.1) (2024-03-07)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [19.0.0](https://github.com/invertase/react-native-firebase/compare/v18.9.0...v19.0.0) (2024-02-26)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [18.9.0](https://github.com/invertase/react-native-firebase/compare/v18.8.0...v18.9.0) (2024-02-21)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [18.8.0](https://github.com/invertase/react-native-firebase/compare/v18.7.3...v18.8.0) (2024-01-25)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [18.7.3](https://github.com/invertase/react-native-firebase/compare/v18.7.2...v18.7.3) (2023-12-13)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [18.7.2](https://github.com/invertase/react-native-firebase/compare/v18.7.1...v18.7.2) (2023-12-08)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [18.7.1](https://github.com/invertase/react-native-firebase/compare/v18.7.0...v18.7.1) (2023-11-29)

### Bug Fixes

- **ios): Revert "build(ios:** specify our script phases always run" ([62b44d6](https://github.com/invertase/react-native-firebase/commit/62b44d68d3794e701e173c9f1a97e131844f0406))

## [18.7.0](https://github.com/invertase/react-native-firebase/compare/v18.6.2...v18.7.0) (2023-11-28)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [18.6.2](https://github.com/invertase/react-native-firebase/compare/v18.6.1...v18.6.2) (2023-11-23)

### Bug Fixes

- adopt firebase-ios-sdk 10.18.0 / firebase-android-sdk 32.6.0 ([6a8b25b](https://github.com/invertase/react-native-firebase/commit/6a8b25bc1ed22860d1cef8fa3507ca5df3a28420))

## [18.6.1](https://github.com/invertase/react-native-firebase/compare/v18.6.0...v18.6.1) (2023-11-01)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [18.6.0](https://github.com/invertase/react-native-firebase/compare/v18.5.0...v18.6.0) (2023-10-26)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [18.5.0](https://github.com/invertase/react-native-firebase/compare/v18.4.0...v18.5.0) (2023-09-22)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [18.4.0](https://github.com/invertase/react-native-firebase/compare/v18.3.2...v18.4.0) (2023-09-11)

### Features

- **crashlytics:** Firbase web modular V9 API ([#7283](https://github.com/invertase/react-native-firebase/issues/7283)) ([57f7327](https://github.com/invertase/react-native-firebase/commit/57f7327da4b405dace6b040e8a942d5f107f3603))

## [18.3.2](https://github.com/invertase/react-native-firebase/compare/v18.3.1...v18.3.2) (2023-09-02)

### Bug Fixes

- **app, sdks:** adopt firebase-android-sdk 32.2.3 ([129d6ef](https://github.com/invertase/react-native-firebase/commit/129d6ef1eb1b45be3390687a002bddfe87386fa3))

## [18.3.1](https://github.com/invertase/react-native-firebase/compare/v18.3.0...v18.3.1) (2023-08-23)

### Bug Fixes

- **app, sdks:** adopt android-sdk 32.2.2 / ios-sdk 10.13.0 ([5484c0b](https://github.com/invertase/react-native-firebase/commit/5484c0b69420f888f9a3a59aec8cc59d45f1d2d6))

## [18.3.0](https://github.com/invertase/react-native-firebase/compare/v18.2.0...v18.3.0) (2023-07-19)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [18.2.0](https://github.com/invertase/react-native-firebase/compare/v18.1.0...v18.2.0) (2023-07-13)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [18.1.0](https://github.com/invertase/react-native-firebase/compare/v18.0.0...v18.1.0) (2023-06-22)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [18.0.0](https://github.com/invertase/react-native-firebase/compare/v17.5.0...v18.0.0) (2023-06-05)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [17.5.0](https://github.com/invertase/react-native-firebase/compare/v17.4.3...v17.5.0) (2023-05-11)

**Note:** Version bump only for package @react-native-firebase/crashlytics

### [17.4.3](https://github.com/invertase/react-native-firebase/compare/v17.4.2...v17.4.3) (2023-04-26)

### Bug Fixes

- **expo:** update dependencies of config plugins ([3e81143](https://github.com/invertase/react-native-firebase/commit/3e81143e67028f70c20530b8e1083b2a904f96f4))

### [17.4.2](https://github.com/invertase/react-native-firebase/compare/v17.4.1...v17.4.2) (2023-04-05)

**Note:** Version bump only for package @react-native-firebase/crashlytics

### [17.4.1](https://github.com/invertase/react-native-firebase/compare/v17.4.0...v17.4.1) (2023-04-01)

### Bug Fixes

- **crashlytics, android:** use v2.9.2 of crashlytics android plugin ([8460ab6](https://github.com/invertase/react-native-firebase/commit/8460ab6176bb0d287a277853427d94004c30a4d0)), closes [#6983](https://github.com/invertase/react-native-firebase/issues/6983)

## [17.4.0](https://github.com/invertase/react-native-firebase/compare/v17.3.2...v17.4.0) (2023-03-25)

**Note:** Version bump only for package @react-native-firebase/crashlytics

### [17.3.2](https://github.com/invertase/react-native-firebase/compare/v17.3.1...v17.3.2) (2023-03-05)

**Note:** Version bump only for package @react-native-firebase/crashlytics

### [17.3.1](https://github.com/invertase/react-native-firebase/compare/v17.3.0...v17.3.1) (2023-02-23)

### Bug Fixes

- **app, android:** adopt firebase-android-sdk 31.2.2 w/crash fixes ([2d1f2cb](https://github.com/invertase/react-native-firebase/commit/2d1f2cb64d6460a6a73aeea57b4472060801aecb)), closes [#6930](https://github.com/invertase/react-native-firebase/issues/6930)

## [17.3.0](https://github.com/invertase/react-native-firebase/compare/v17.2.0...v17.3.0) (2023-02-15)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [17.2.0](https://github.com/invertase/react-native-firebase/compare/v17.1.0...v17.2.0) (2023-02-15)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [17.1.0](https://github.com/invertase/react-native-firebase/compare/v17.0.0...v17.1.0) (2023-02-09)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [17.0.0](https://github.com/invertase/react-native-firebase/compare/v16.7.0...v17.0.0) (2023-02-02)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [16.7.0](https://github.com/invertase/react-native-firebase/compare/v16.6.0...v16.7.0) (2023-01-28)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [16.6.0](https://github.com/invertase/react-native-firebase/compare/v16.5.2...v16.6.0) (2023-01-27)

**Note:** Version bump only for package @react-native-firebase/crashlytics

### [16.5.2](https://github.com/invertase/react-native-firebase/compare/v16.5.1...v16.5.2) (2023-01-23)

**Note:** Version bump only for package @react-native-firebase/crashlytics

### [16.5.1](https://github.com/invertase/react-native-firebase/compare/v16.5.0...v16.5.1) (2023-01-20)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [16.5.0](https://github.com/invertase/react-native-firebase/compare/v16.4.6...v16.5.0) (2022-12-16)

**Note:** Version bump only for package @react-native-firebase/crashlytics

### [16.4.6](https://github.com/invertase/react-native-firebase/compare/v16.4.5...v16.4.6) (2022-11-18)

**Note:** Version bump only for package @react-native-firebase/crashlytics

### [16.4.5](https://github.com/invertase/react-native-firebase/compare/v16.4.4...v16.4.5) (2022-11-16)

**Note:** Version bump only for package @react-native-firebase/crashlytics

### [16.4.4](https://github.com/invertase/react-native-firebase/compare/v16.4.3...v16.4.4) (2022-11-14)

**Note:** Version bump only for package @react-native-firebase/crashlytics

### [16.4.3](https://github.com/invertase/react-native-firebase/compare/v16.4.2...v16.4.3) (2022-11-06)

**Note:** Version bump only for package @react-native-firebase/crashlytics

### [16.4.2](https://github.com/invertase/react-native-firebase/compare/v16.4.1...v16.4.2) (2022-11-04)

**Note:** Version bump only for package @react-native-firebase/crashlytics

### [16.4.1](https://github.com/invertase/react-native-firebase/compare/v16.4.0...v16.4.1) (2022-11-02)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [16.4.0](https://github.com/invertase/react-native-firebase/compare/v16.3.1...v16.4.0) (2022-10-30)

**Note:** Version bump only for package @react-native-firebase/crashlytics

### [16.3.1](https://github.com/invertase/react-native-firebase/compare/v16.3.0...v16.3.1) (2022-10-28)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [16.3.0](https://github.com/invertase/react-native-firebase/compare/v16.2.0...v16.3.0) (2022-10-26)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [16.2.0](https://github.com/invertase/react-native-firebase/compare/v16.1.1...v16.2.0) (2022-10-23)

**Note:** Version bump only for package @react-native-firebase/crashlytics

### [16.1.1](https://github.com/invertase/react-native-firebase/compare/v16.1.0...v16.1.1) (2022-10-21)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [16.1.0](https://github.com/invertase/react-native-firebase/compare/v16.0.0...v16.1.0) (2022-10-20)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [16.0.0](https://github.com/invertase/react-native-firebase/compare/v15.7.1...v16.0.0) (2022-10-19)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [15.7.1](https://github.com/invertase/react-native-firebase/compare/v15.7.0...v15.7.1) (2022-10-19)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [15.7.0](https://github.com/invertase/react-native-firebase/compare/v15.6.0...v15.7.0) (2022-10-01)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [15.6.0](https://github.com/invertase/react-native-firebase/compare/v15.5.0...v15.6.0) (2022-09-17)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [15.5.0](https://github.com/invertase/react-native-firebase/compare/v15.4.0...v15.5.0) (2022-09-16)

### Features

- **android:** firebase-android-sdk 30.5.0 ([abe7620](https://github.com/invertase/react-native-firebase/commit/abe7620c35cd91bd105d64fa64777868a3482435))

# [15.4.0](https://github.com/invertase/react-native-firebase/compare/v15.3.0...v15.4.0) (2022-08-27)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [15.3.0](https://github.com/invertase/react-native-firebase/compare/v15.2.0...v15.3.0) (2022-08-07)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [15.2.0](https://github.com/invertase/react-native-firebase/compare/v15.1.1...v15.2.0) (2022-07-21)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [15.1.1](https://github.com/invertase/react-native-firebase/compare/v15.1.0...v15.1.1) (2022-06-28)

### Bug Fixes

- **ios, crashlytics:** depend on FirebaseCoreExtension ([#6352](https://github.com/invertase/react-native-firebase/issues/6352)) ([ea0ffe0](https://github.com/invertase/react-native-firebase/commit/ea0ffe06e7c6182bf38b18e9d6ca00c388ec6893)), closes [/github.com/invertase/react-native-firebase/issues/6322#issuecomment-1168902482](https://github.com//github.com/invertase/react-native-firebase/issues/6322/issues/issuecomment-1168902482)

# [15.1.0](https://github.com/invertase/react-native-firebase/compare/v15.0.0...v15.1.0) (2022-06-28)

### Features

- **android, sdk:** use firebase-android-sdk 30.2.0 ([66e6fb0](https://github.com/invertase/react-native-firebase/commit/66e6fb0885c4f2885aeec140a9c0655a5eedd8df))

# [15.0.0](https://github.com/invertase/react-native-firebase/compare/v14.11.1...v15.0.0) (2022-06-20)

### Bug Fixes

- **crashlytics, ios:** forward port to firebase-ios-sdk v9 header locations ([e5bd716](https://github.com/invertase/react-native-firebase/commit/e5bd7161c0d1142da184e0e676c8756e2ebebf90))

## [14.11.1](https://github.com/invertase/react-native-firebase/compare/v14.11.0...v14.11.1) (2022-06-17)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [14.11.0](https://github.com/invertase/react-native-firebase/compare/v14.10.1...v14.11.0) (2022-05-27)

### Features

- **android, sdk:** firebase-android-sdk 30.1.0 ([b0462d4](https://github.com/invertase/react-native-firebase/commit/b0462d4d34d1518a50daeca09288bf4aa0e0f695))

## [14.10.1](https://github.com/invertase/react-native-firebase/compare/v14.10.0...v14.10.1) (2022-05-26)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [14.10.0](https://github.com/invertase/react-native-firebase/compare/v14.9.4...v14.10.0) (2022-05-26)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [14.9.4](https://github.com/invertase/react-native-firebase/compare/v14.9.3...v14.9.4) (2022-05-14)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [14.9.3](https://github.com/invertase/react-native-firebase/compare/v14.9.2...v14.9.3) (2022-05-10)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [14.9.2](https://github.com/invertase/react-native-firebase/compare/v14.9.1...v14.9.2) (2022-05-10)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [14.9.1](https://github.com/invertase/react-native-firebase/compare/v14.9.0...v14.9.1) (2022-04-28)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [14.9.0](https://github.com/invertase/react-native-firebase/compare/v14.8.1...v14.9.0) (2022-04-27)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [14.8.1](https://github.com/invertase/react-native-firebase/compare/v14.8.0...v14.8.1) (2022-04-25)

### Bug Fixes

- **app, expo:** Support RN 0.68 Obj-C++ AppDelegate ([#6213](https://github.com/invertase/react-native-firebase/issues/6213)) ([6f2d7e1](https://github.com/invertase/react-native-firebase/commit/6f2d7e1608d04613b77461f9647802aa1058e6cc))

# [14.8.0](https://github.com/invertase/react-native-firebase/compare/v14.7.0...v14.8.0) (2022-04-19)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [14.7.0](https://github.com/invertase/react-native-firebase/compare/v14.6.0...v14.7.0) (2022-03-23)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [14.6.0](https://github.com/invertase/react-native-firebase/compare/v14.5.1...v14.6.0) (2022-03-23)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [14.5.1](https://github.com/invertase/react-native-firebase/compare/v14.5.0...v14.5.1) (2022-03-05)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [14.5.0](https://github.com/invertase/react-native-firebase/compare/v14.4.0...v14.5.0) (2022-02-15)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [14.4.0](https://github.com/invertase/react-native-firebase/compare/v14.3.3...v14.4.0) (2022-02-13)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [14.3.3](https://github.com/invertase/react-native-firebase/compare/v14.3.2...v14.3.3) (2022-02-12)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [14.3.2](https://github.com/invertase/react-native-firebase/compare/v14.3.1...v14.3.2) (2022-02-10)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [14.3.1](https://github.com/invertase/react-native-firebase/compare/v14.3.0...v14.3.1) (2022-02-07)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [14.3.0](https://github.com/invertase/react-native-firebase/compare/v14.2.4...v14.3.0) (2022-01-26)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [14.2.4](https://github.com/invertase/react-native-firebase/compare/v14.2.3...v14.2.4) (2022-01-24)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [14.2.3](https://github.com/invertase/react-native-firebase/compare/v14.2.2...v14.2.3) (2022-01-20)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [14.2.2](https://github.com/invertase/react-native-firebase/compare/v14.2.1...v14.2.2) (2022-01-06)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [14.2.1](https://github.com/invertase/react-native-firebase/compare/v14.2.0...v14.2.1) (2021-12-31)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [14.2.0](https://github.com/invertase/react-native-firebase/compare/v14.1.0...v14.2.0) (2021-12-31)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [14.1.0](https://github.com/invertase/react-native-firebase/compare/v14.0.1...v14.1.0) (2021-12-18)

### Bug Fixes

- **crashlytics, ios:** alter header import style for Expo SDK 44 compat ([#5947](https://github.com/invertase/react-native-firebase/issues/5947)) ([e45f37c](https://github.com/invertase/react-native-firebase/commit/e45f37cf76eba80f5fd537b6b7806c79f7052a74))

## [14.0.1](https://github.com/invertase/react-native-firebase/compare/v14.0.0...v14.0.1) (2021-12-15)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [14.0.0](https://github.com/invertase/react-native-firebase/compare/v13.1.1...v14.0.0) (2021-12-14)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [13.1.1](https://github.com/invertase/react-native-firebase/compare/v13.1.0...v13.1.1) (2021-12-14)

### Bug Fixes

- **deps:** AGP7.0.4, firebase-android-sdk 29.0.2, javascript deps ([55d0a36](https://github.com/invertase/react-native-firebase/commit/55d0a36a0addc54e347f26bb8ee88bb38b0fa4a6))

# [13.1.0](https://github.com/invertase/react-native-firebase/compare/v13.0.1...v13.1.0) (2021-12-02)

### Features

- **sdks:** firebase-ios-sdk 8.10.0 / firebase-android-sdk 29.0.1 ([f6949c9](https://github.com/invertase/react-native-firebase/commit/f6949c9f3669df6d8b3f78bbee97bee2f36b7df3))

## [13.0.1](https://github.com/invertase/react-native-firebase/compare/v13.0.0...v13.0.1) (2021-11-05)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [13.0.0](https://github.com/invertase/react-native-firebase/compare/v12.9.3...v13.0.0) (2021-10-31)

### Bug Fixes

- rename default branch to main ([25e1d3d](https://github.com/invertase/react-native-firebase/commit/25e1d3d5a1a8311588938dc9d8fdf71d11cd9963))

- feat(sdks, android)!: firebase-android-sdk v29 / minSdkVersion API19 / target+compile API31 (#5825) ([f60afe1](https://github.com/invertase/react-native-firebase/commit/f60afe158b2dc823bd7169e36c3e428470576c7e)), closes [#5825](https://github.com/invertase/react-native-firebase/issues/5825)

### BREAKING CHANGES

- firebase-android-sdk 29 requires android/build.gradle minSdkVersion 19 (as required in react-native 0.64+)

## [12.9.3](https://github.com/invertase/react-native-firebase/compare/v12.9.2...v12.9.3) (2021-10-22)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [12.9.2](https://github.com/invertase/react-native-firebase/compare/v12.9.1...v12.9.2) (2021-10-17)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [12.9.1](https://github.com/invertase/react-native-firebase/compare/v12.9.0...v12.9.1) (2021-10-10)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [12.9.0](https://github.com/invertase/react-native-firebase/compare/v12.8.0...v12.9.0) (2021-10-03)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [12.8.0](https://github.com/invertase/react-native-firebase/compare/v12.7.5...v12.8.0) (2021-09-14)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [12.7.5](https://github.com/invertase/react-native-firebase/compare/v12.7.4...v12.7.5) (2021-09-04)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [12.7.4](https://github.com/invertase/react-native-firebase/compare/v12.7.3...v12.7.4) (2021-08-31)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [12.7.3](https://github.com/invertase/react-native-firebase/compare/v12.7.2...v12.7.3) (2021-08-24)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [12.7.2](https://github.com/invertase/react-native-firebase/compare/v12.7.1...v12.7.2) (2021-08-21)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [12.7.1](https://github.com/invertase/react-native-firebase/compare/v12.7.0...v12.7.1) (2021-08-20)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [12.7.0](https://github.com/invertase/react-native-firebase/compare/v12.6.1...v12.7.0) (2021-08-19)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [12.6.1](https://github.com/invertase/react-native-firebase/compare/v12.6.0...v12.6.1) (2021-08-17)

### Bug Fixes

- **crashlytics, config:** handle new app_data_collection_default_enabled key ([81aa17f](https://github.com/invertase/react-native-firebase/commit/81aa17f1b60a6171329d9a2f250226010dfc081e))

# [12.6.0](https://github.com/invertase/react-native-firebase/compare/v12.5.0...v12.6.0) (2021-08-16)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [12.5.0](https://github.com/invertase/react-native-firebase/compare/v12.4.0...v12.5.0) (2021-08-12)

### Bug Fixes

- **expo:** do not publish plugin tests and sources ([#5565](https://github.com/invertase/react-native-firebase/issues/5565)) ([6b5dca5](https://github.com/invertase/react-native-firebase/commit/6b5dca500ea413ee68acf8abc74e579f4298cbad))

# [12.4.0](https://github.com/invertase/react-native-firebase/compare/v12.3.0...v12.4.0) (2021-07-29)

### Features

- Add Expo config plugin ([#5480](https://github.com/invertase/react-native-firebase/issues/5480)) ([832057c](https://github.com/invertase/react-native-firebase/commit/832057cfbdf1778ad2141a1ad4466d2e8c24b8ce))

# [12.3.0](https://github.com/invertase/react-native-firebase/compare/v12.2.0...v12.3.0) (2021-07-21)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [12.2.0](https://github.com/invertase/react-native-firebase/compare/v12.1.0...v12.2.0) (2021-07-16)

### Features

- **crashlytics:** add helper methods for log and setCustomKey ([06d515c](https://github.com/invertase/react-native-firebase/commit/06d515cad533c76328e324f0e950a814881aab0d))

# [12.1.0](https://github.com/invertase/react-native-firebase/compare/v12.0.0...v12.1.0) (2021-06-11)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [12.0.0](https://github.com/invertase/react-native-firebase/compare/v11.5.0...v12.0.0) (2021-05-19)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [11.5.0](https://github.com/invertase/react-native-firebase/compare/v11.4.1...v11.5.0) (2021-05-12)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [11.4.1](https://github.com/invertase/react-native-firebase/compare/v11.4.0...v11.4.1) (2021-04-29)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [11.4.0](https://github.com/invertase/react-native-firebase/compare/v11.3.3...v11.4.0) (2021-04-29)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [11.3.3](https://github.com/invertase/react-native-firebase/compare/v11.3.2...v11.3.3) (2021-04-24)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [11.3.2](https://github.com/invertase/react-native-firebase/compare/v11.3.1...v11.3.2) (2021-04-19)

### Bug Fixes

- **all, android:** purge jcenter() from android build ([2c6a6a8](https://github.com/invertase/react-native-firebase/commit/2c6a6a82ec363fd948ea880fd397acb886c97453))

## [11.3.1](https://github.com/invertase/react-native-firebase/compare/v11.3.0...v11.3.1) (2021-04-18)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [11.3.0](https://github.com/invertase/react-native-firebase/compare/v11.2.0...v11.3.0) (2021-04-16)

### Bug Fixes

- **crashlytics, debug:** Disable Crashlytics in debug mode by default ([#5117](https://github.com/invertase/react-native-firebase/issues/5117)) ([eeeba2e](https://github.com/invertase/react-native-firebase/commit/eeeba2ed771b72a04dd9b2154c259a8648a21022))
- **crashlytics, ios:** register library with dynamic version string ([90bceb2](https://github.com/invertase/react-native-firebase/commit/90bceb292bfcbdf16517b654376d151c26e5432c))
- **crashlytics, ios:** warn if debugger will break crashlytics ([d6b6d23](https://github.com/invertase/react-native-firebase/commit/d6b6d231d4c4da68219e52fe8bc9e0220f73ef0c))

### Features

- **crashlytics:** add configuration to exception handler chaining behavior ([4c640ff](https://github.com/invertase/react-native-firebase/commit/4c640ff52e1fb692bddcbeb76a2ff2a302e56334))
- **crashlytics:** flag fatal errors for crashlytics and analytics ([c94546d](https://github.com/invertase/react-native-firebase/commit/c94546d8127606dca5bfd09ef92ec32eec333f19))

# [11.2.0](https://github.com/invertase/react-native-firebase/compare/v11.1.2...v11.2.0) (2021-03-26)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [11.1.2](https://github.com/invertase/react-native-firebase/compare/v11.1.1...v11.1.2) (2021-03-17)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [11.1.1](https://github.com/invertase/react-native-firebase/compare/v11.1.0...v11.1.1) (2021-03-16)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [11.1.0](https://github.com/invertase/react-native-firebase/compare/v11.0.0...v11.1.0) (2021-03-13)

### Features

- **crashlytics, native:** add non-fatal exception logger for 3rd party native code use ([#5015](https://github.com/invertase/react-native-firebase/issues/5015)) ([b3e6810](https://github.com/invertase/react-native-firebase/commit/b3e681079af0bcc00655d079823a7ec6442d8723))

# [11.0.0](https://github.com/invertase/react-native-firebase/compare/v10.8.1...v11.0.0) (2021-03-03)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [10.8.1](https://github.com/invertase/react-native-firebase/compare/v10.8.0...v10.8.1) (2021-02-22)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [10.8.0](https://github.com/invertase/react-native-firebase/compare/v10.7.0...v10.8.0) (2021-02-13)

### Features

- **crashlytics:** add JS exception non-fatal error generation toggle ([#4904](https://github.com/invertase/react-native-firebase/issues/4904)) ([63c35b3](https://github.com/invertase/react-native-firebase/commit/63c35b3d9243a76fd77dedaa9fa83fca7fb802ae))

# [10.7.0](https://github.com/invertase/react-native-firebase/compare/v10.6.4...v10.7.0) (2021-02-09)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [10.6.4](https://github.com/invertase/react-native-firebase/compare/v10.6.3...v10.6.4) (2021-02-05)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [10.6.3](https://github.com/invertase/react-native-firebase/compare/v10.6.2...v10.6.3) (2021-02-05)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [10.6.1](https://github.com/invertase/react-native-firebase/compare/v10.6.0...v10.6.1) (2021-02-04)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [10.6.0](https://github.com/invertase/react-native-firebase/compare/v10.5.1...v10.6.0) (2021-02-04)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [10.5.1](https://github.com/invertase/react-native-firebase/compare/v10.5.0...v10.5.1) (2021-01-19)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [10.4.1](https://github.com/invertase/react-native-firebase/compare/v10.4.0...v10.4.1) (2021-01-08)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [10.4.0](https://github.com/invertase/react-native-firebase/compare/v10.3.1...v10.4.0) (2020-12-30)

### Bug Fixes

- **ios:** bump ios min deployment to ios10 - remnant from [#4471](https://github.com/invertase/react-native-firebase/issues/4471) ([4a57578](https://github.com/invertase/react-native-firebase/commit/4a5757827789141600625eebe5e13c976ddb7402))

## [10.3.1](https://github.com/invertase/react-native-firebase/compare/v10.3.0...v10.3.1) (2020-12-18)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [10.3.0](https://github.com/invertase/react-native-firebase/compare/v10.2.0...v10.3.0) (2020-12-18)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [10.2.0](https://github.com/invertase/react-native-firebase/compare/v10.1.1...v10.2.0) (2020-12-11)

### Features

- firebase-ios-sdk 7.2.0 / firebase-android-sdk 26.1.1 ([#4648](https://github.com/invertase/react-native-firebase/issues/4648)) ([a158a74](https://github.com/invertase/react-native-firebase/commit/a158a74dee0dd6774c725ff1213453f8dfdcb8f5))

## [10.1.1](https://github.com/invertase/react-native-firebase/compare/v10.1.0...v10.1.1) (2020-12-02)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [10.1.0](https://github.com/invertase/react-native-firebase/compare/v10.0.0...v10.1.0) (2020-11-26)

### Features

- **crashlytics:** add custom message ability to javascript stack traces ([#4609](https://github.com/invertase/react-native-firebase/issues/4609)) ([afaa95d](https://github.com/invertase/react-native-firebase/commit/afaa95dbf4c744cb04042f6236837164edc8bbb8))

# [10.0.0](https://github.com/invertase/react-native-firebase/compare/fc8c4c0622f8e6814879d0306f66012df5b83cd8...v10.0.0) (2020-11-17)

### BREAKING CHANGES

- breaking change to mark new internal versioning requirements.

## [8.5.2](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.5.1...@react-native-firebase/crashlytics@8.5.2) (2020-11-10)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [8.5.1](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.5.0...@react-native-firebase/crashlytics@8.5.1) (2020-11-10)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [8.5.0](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.4.12...@react-native-firebase/crashlytics@8.5.0) (2020-11-10)

### Features

- **crashlytics, ios:** put input files when pod install. ([#4520](https://github.com/invertase/react-native-firebase/issues/4520)) ([f2161fd](https://github.com/invertase/react-native-firebase/commit/f2161fddbab68e01c0b0653201be492def43df3b))

## [8.4.12](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.4.11...@react-native-firebase/crashlytics@8.4.12) (2020-10-30)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [8.4.11](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.4.10...@react-native-firebase/crashlytics@8.4.11) (2020-10-21)

### Bug Fixes

- **crashlytics, ios:** generate uncatchable crash for iOS crash testing ([#4426](https://github.com/invertase/react-native-firebase/issues/4426)) ([2dcaad5](https://github.com/invertase/react-native-firebase/commit/2dcaad59c27b90b1f2b3ef6b31e46d3eac8a5e2e))

## [8.4.10](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.4.9...@react-native-firebase/crashlytics@8.4.10) (2020-10-16)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [8.4.9](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.4.8...@react-native-firebase/crashlytics@8.4.9) (2020-09-30)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [8.4.8](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.4.7...@react-native-firebase/crashlytics@8.4.8) (2020-09-30)

### Bug Fixes

- **types:** enable TypeScript libCheck & resolve type conflicts ([#4306](https://github.com/invertase/react-native-firebase/issues/4306)) ([aa8ee8b](https://github.com/invertase/react-native-firebase/commit/aa8ee8b7e83443d2c1664993800e15faf4b59b0e))

## [8.4.7](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.4.6...@react-native-firebase/crashlytics@8.4.7) (2020-09-30)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [8.4.6](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.4.5...@react-native-firebase/crashlytics@8.4.6) (2020-09-17)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [8.4.5](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.4.4...@react-native-firebase/crashlytics@8.4.5) (2020-09-17)

### Bug Fixes

- **ios, podspec:** depend on React-Core instead of React ([#4275](https://github.com/invertase/react-native-firebase/issues/4275)) ([fd1a2be](https://github.com/invertase/react-native-firebase/commit/fd1a2be6b6ab1dec89e5dce1fc237435c3e1d510))

## [8.4.4](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.4.3...@react-native-firebase/crashlytics@8.4.4) (2020-09-11)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [8.4.3](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.4.2...@react-native-firebase/crashlytics@8.4.3) (2020-09-11)

### Bug Fixes

- **crashlytics, ios:** explicitly set collection opt in/out ([#4236](https://github.com/invertase/react-native-firebase/issues/4236)) ([cda4c10](https://github.com/invertase/react-native-firebase/commit/cda4c1012737eab8b64e8f8593b623771f5b2734))

## [8.4.2](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.4.1...@react-native-firebase/crashlytics@8.4.2) (2020-08-28)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [8.4.1](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.4.0...@react-native-firebase/crashlytics@8.4.1) (2020-08-26)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [8.4.0](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.3.4...@react-native-firebase/crashlytics@8.4.0) (2020-08-26)

### Features

- bump firebase sdk versions, add GoogleApi dep, use Android API29 ([#4122](https://github.com/invertase/react-native-firebase/issues/4122)) ([728f418](https://github.com/invertase/react-native-firebase/commit/728f41863832d21230c6eb1f55385284fef03c09))

## [8.3.4](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.3.3...@react-native-firebase/crashlytics@8.3.4) (2020-08-25)

### Bug Fixes

- **ios, crashlytics:** allow Crashlytics inclusion w/o Analytics ([#4134](https://github.com/invertase/react-native-firebase/issues/4134)) ([e023b71](https://github.com/invertase/react-native-firebase/commit/e023b71486d6834ba175e91ee5809af36a03588d))

## [8.3.3](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.3.2...@react-native-firebase/crashlytics@8.3.3) (2020-08-24)

### Bug Fixes

- **ios, crashlytics:** use NSInternalInconsistencyException to crash w/o redbox ([#4126](https://github.com/invertase/react-native-firebase/issues/4126)) ([2cbab5c](https://github.com/invertase/react-native-firebase/commit/2cbab5cf91f4e8542c30a237637d071c14bbcde5))

## [8.3.2](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.3.1...@react-native-firebase/crashlytics@8.3.2) (2020-08-20)

### Bug Fixes

- **ios, crashlytics:** use new recommended manual crash style ([#4111](https://github.com/invertase/react-native-firebase/issues/4111)) ([6b136c3](https://github.com/invertase/react-native-firebase/commit/6b136c3972eb25ad37b4d6230e1d6e139c094f86))

## [8.3.1](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.3.0...@react-native-firebase/crashlytics@8.3.1) (2020-08-15)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [8.3.0](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.2.1...@react-native-firebase/crashlytics@8.3.0) (2020-08-03)

### Features

- **crashlytics:** add new APIs `checkForUnsentReports`, `deleteUnsentReports`,`didCrashOnPreviousExecution`,`sendUnsentReports` ([#4009](https://github.com/invertase/react-native-firebase/issues/4009)) ([52eeed3](https://github.com/invertase/react-native-firebase/commit/52eeed31b3436b0f90767298dcc515b0897ba942))

## [8.2.1](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.2.0...@react-native-firebase/crashlytics@8.2.1) (2020-08-03)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [8.2.0](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.1.2...@react-native-firebase/crashlytics@8.2.0) (2020-08-03)

### Features

- use latest android & ios Firebase SDKs version ([#3956](https://github.com/invertase/react-native-firebase/issues/3956)) ([e7b4bb3](https://github.com/invertase/react-native-firebase/commit/e7b4bb31b05985c044b1f01625a43e364bb653ef))

## [8.1.2](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.1.1...@react-native-firebase/crashlytics@8.1.2) (2020-07-09)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [8.1.1](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.1.0...@react-native-firebase/crashlytics@8.1.1) (2020-07-07)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [8.1.0](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.0.3...@react-native-firebase/crashlytics@8.1.0) (2020-07-07)

### Features

- **android,ios:** upgrade native SDK versions ([#3881](https://github.com/invertase/react-native-firebase/issues/3881)) ([6cb68a8](https://github.com/invertase/react-native-firebase/commit/6cb68a8ea808392fac3a28bdb1a76049c7b52e86))

## [8.0.3](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.0.2...@react-native-firebase/crashlytics@8.0.3) (2020-07-05)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [8.0.2](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.0.1...@react-native-firebase/crashlytics@8.0.2) (2020-07-05)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [8.0.1](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@8.0.0...@react-native-firebase/crashlytics@8.0.1) (2020-06-30)

**Note:** Version bump only for package @react-native-firebase/crashlytics

# [8.0.0](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@7.1.7...@react-native-firebase/crashlytics@8.0.0) (2020-06-30)

- feat(crashlytics)!: upgrade to new Firebase Crashlytics SDK (#3580) ([cad58e1](https://github.com/invertase/react-native-firebase/commit/cad58e178b43dea461e17fa4a0a3fecd507ba68a)), closes [#3580](https://github.com/invertase/react-native-firebase/issues/3580)

### BREAKING CHANGES

- This is a breaking change to remove the use of the Fabric SDKs.

## [7.1.7](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@7.1.6...@react-native-firebase/crashlytics@7.1.7) (2020-06-26)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [7.1.6](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@7.1.5...@react-native-firebase/crashlytics@7.1.6) (2020-06-22)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [7.1.5](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@7.1.4...@react-native-firebase/crashlytics@7.1.5) (2020-06-10)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [7.1.4](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@7.1.3...@react-native-firebase/crashlytics@7.1.4) (2020-06-03)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [7.1.3](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@7.1.2...@react-native-firebase/crashlytics@7.1.3) (2020-05-29)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [7.1.2](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@7.1.1...@react-native-firebase/crashlytics@7.1.2) (2020-05-29)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [7.1.1](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@7.1.0...@react-native-firebase/crashlytics@7.1.1) (2020-05-29)

### Bug Fixes

- **android:** remove deprecated usages of `APPLICATION_ID` ([#3711](https://github.com/invertase/react-native-firebase/issues/3711)) ([984d3fc](https://github.com/invertase/react-native-firebase/commit/984d3fc1668221c166ab459d67d1c646d73d165b))

# [7.1.0](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@7.0.1...@react-native-firebase/crashlytics@7.1.0) (2020-05-22)

### Features

- update native Firebase SDK versions ([#3663](https://github.com/invertase/react-native-firebase/issues/3663)) ([4db9dbc](https://github.com/invertase/react-native-firebase/commit/4db9dbc3ec20bf96de0efad15000f00b41e4a799))

## [7.0.1](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@7.0.0...@react-native-firebase/crashlytics@7.0.1) (2020-05-13)

**Note:** Version bump only for package @react-native-firebase/crashlytics

## [7.0.0](https://github.com/invertase/react-native-firebase/compare/@react-native-firebase/crashlytics@7.0.0...@react-native-firebase/crashlytics@7.0.0) (2020-05-13)

- feat!: all packages should depend on core (#3613) ([252a423](https://github.com/invertase/react-native-firebase/commit/252a4239e98a0f2a55c4afcd2d82e4d5f97e65e9)), closes [#3613](https://github.com/invertase/react-native-firebase/issues/3613)

### Features

- **ios:** podspecs now utilize CoreOnly instead of Core ([#3575](https://github.com/invertase/react-native-firebase/issues/3575)) ([35285f1](https://github.com/invertase/react-native-firebase/commit/35285f1655b16d05e6630fc556f95cccfb707ee4))

### BREAKING CHANGES

- breaking change to mark new internal versioning requirements.
