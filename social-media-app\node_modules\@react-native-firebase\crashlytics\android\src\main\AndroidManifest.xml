<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  package="io.invertase.firebase.crashlytics">
  <application>
    <!-- Disable crashlytics by default so we can custom init with CrashlyticsNdk support -->
    <meta-data
      android:name="firebase_crashlytics_collection_enabled"
      android:value="false" />
    <provider
      android:name="io.invertase.firebase.crashlytics.ReactNativeFirebaseCrashlyticsInitProvider"
      android:authorities="${applicationId}.reactnativefirebasecrashlyticsinitprovider"
      android:exported="false"
      android:initOrder="98" /> <!-- Firebase = 100, ReactNativeFirebase = 99, runs after both (highest first) -->
  </application>
</manifest>
