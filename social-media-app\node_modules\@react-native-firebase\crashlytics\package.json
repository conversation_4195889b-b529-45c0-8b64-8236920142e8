{"name": "@react-native-firebase/crashlytics", "version": "22.2.0", "author": "Invertase <<EMAIL>> (http://invertase.io)", "description": "React Native Firebase - Firebase Crashlytics is a lightweight, realtime crash reporter that helps you track, prioritize, and fix stability issues that erode your app quality. React Native Firebase provides automatic crash reporting for both native and JavaScript errors, including unhandled promise rejections.", "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"build": "genversion --semi lib/version.js", "build:clean": "rimraf android/build && rimraf ios/build", "build:plugin": "rimraf plugin/build && tsc --build plugin", "lint:plugin": "eslint plugin/src/*", "prepare": "yarn run build && yarn run build:plugin"}, "repository": {"type": "git", "url": "https://github.com/invertase/react-native-firebase/tree/main/packages/crashlytics"}, "license": "Apache-2.0", "keywords": ["react", "react-native", "firebase", "fabric", "crash", "bug", "error", "reporting", "crashlytics"], "peerDependencies": {"@react-native-firebase/app": "22.2.0", "expo": ">=47.0.0"}, "dependencies": {"stacktrace-js": "^2.0.2"}, "devDependencies": {"expo": "^52.0.46"}, "peerDependenciesMeta": {"expo": {"optional": true}}, "publishConfig": {"access": "public", "provenance": true}, "gitHead": "b302210509ceac8078b5fb9fd0b24e68f0641c6a"}