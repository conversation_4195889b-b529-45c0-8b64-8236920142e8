!function(t,e){"use strict";"function"==typeof define&&define.amd?define("stackframe",[],e):"object"==typeof exports?module.exports=e():t.StackFrame=e()}(this,function(){"use strict";function t(t){return t.charAt(0).toUpperCase()+t.substring(1)}function e(t){return function(){return this[t]}}var r=["isConstructor","isEval","isNative","isToplevel"],n=["columnNumber","lineNumber"],i=["fileName","functionName","source"],o=r.concat(n,i,["args"],["evalOrigin"]);function a(e){if(e)for(var r=0;r<o.length;r++)void 0!==e[o[r]]&&this["set"+t(o[r])](e[o[r]])}a.prototype={getArgs:function(){return this.args},setArgs:function(t){if("[object Array]"!==Object.prototype.toString.call(t))throw new TypeError("Args must be an Array");this.args=t},getEvalOrigin:function(){return this.evalOrigin},setEvalOrigin:function(t){if(t instanceof a)this.evalOrigin=t;else{if(!(t instanceof Object))throw new TypeError("Eval Origin must be an Object or StackFrame");this.evalOrigin=new a(t)}},toString:function(){var t=this.getFileName()||"",e=this.getLineNumber()||"",r=this.getColumnNumber()||"",n=this.getFunctionName()||"";return this.getIsEval()?t?"[eval] ("+t+":"+e+":"+r+")":"[eval]:"+e+":"+r:n?n+" ("+t+":"+e+":"+r+")":t+":"+e+":"+r}},a.fromString=function(t){var e=t.indexOf("("),r=t.lastIndexOf(")"),n=t.substring(0,e),i=t.substring(e+1,r).split(","),o=t.substring(r+1);if(0===o.indexOf("@"))var s=/@(.+?)(?::(\d+))?(?::(\d+))?$/.exec(o,""),u=s[1],c=s[2],f=s[3];return new a({functionName:n,args:i||void 0,fileName:u,lineNumber:c||void 0,columnNumber:f||void 0})};for(var s=0;s<r.length;s++)a.prototype["get"+t(r[s])]=e(r[s]),a.prototype["set"+t(r[s])]=function(t){return function(e){this[t]=Boolean(e)}}(r[s]);for(var u=0;u<n.length;u++)a.prototype["get"+t(n[u])]=e(n[u]),a.prototype["set"+t(n[u])]=function(t){return function(e){if(r=e,isNaN(parseFloat(r))||!isFinite(r))throw new TypeError(t+" must be a Number");var r;this[t]=Number(e)}}(n[u]);for(var c=0;c<i.length;c++)a.prototype["get"+t(i[c])]=e(i[c]),a.prototype["set"+t(i[c])]=function(t){return function(e){this[t]=String(e)}}(i[c]);return a}),function(t,e){"use strict";"function"==typeof define&&define.amd?define("stack-generator",["stackframe"],e):"object"==typeof exports?module.exports=e(require("stackframe")):t.StackGenerator=e(t.StackFrame)}(this,function(t){return{backtrace:function(e){var r=[],n=10;"object"==typeof e&&"number"==typeof e.maxStackSize&&(n=e.maxStackSize);for(var i=arguments.callee;i&&r.length<n&&i.arguments;){for(var o=new Array(i.arguments.length),a=0;a<o.length;++a)o[a]=i.arguments[a];/function(?:\s+([\w$]+))+\s*\(/.test(i.toString())?r.push(new t({functionName:RegExp.$1||void 0,args:o})):r.push(new t({args:o}));try{i=i.caller}catch(t){break}}return r}}});
//# sourceMappingURL=stack-generator.min.js.map