!function(e,n){"use strict";"function"==typeof define&&define.amd?define("stackframe",[],n):"object"==typeof exports?module.exports=n():e.StackFrame=n()}(this,function(){"use strict";function e(e){return e.charAt(0).toUpperCase()+e.substring(1)}function n(e){return function(){return this[e]}}var r=["isConstructor","isEval","isNative","isToplevel"],t=["columnNumber","lineNumber"],o=["fileName","functionName","source"],i=r.concat(t,o,["args"],["evalOrigin"]);function s(n){if(n)for(var r=0;r<i.length;r++)void 0!==n[i[r]]&&this["set"+e(i[r])](n[i[r]])}s.prototype={getArgs:function(){return this.args},setArgs:function(e){if("[object Array]"!==Object.prototype.toString.call(e))throw new TypeError("Args must be an Array");this.args=e},getEvalOrigin:function(){return this.evalOrigin},setEvalOrigin:function(e){if(e instanceof s)this.evalOrigin=e;else{if(!(e instanceof Object))throw new TypeError("Eval Origin must be an Object or StackFrame");this.evalOrigin=new s(e)}},toString:function(){var e=this.getFileName()||"",n=this.getLineNumber()||"",r=this.getColumnNumber()||"",t=this.getFunctionName()||"";return this.getIsEval()?e?"[eval] ("+e+":"+n+":"+r+")":"[eval]:"+n+":"+r:t?t+" ("+e+":"+n+":"+r+")":e+":"+n+":"+r}},s.fromString=function(e){var n=e.indexOf("("),r=e.lastIndexOf(")"),t=e.substring(0,n),o=e.substring(n+1,r).split(","),i=e.substring(r+1);if(0===i.indexOf("@"))var a=/@(.+?)(?::(\d+))?(?::(\d+))?$/.exec(i,""),u=a[1],l=a[2],c=a[3];return new s({functionName:t,args:o||void 0,fileName:u,lineNumber:l||void 0,columnNumber:c||void 0})};for(var a=0;a<r.length;a++)s.prototype["get"+e(r[a])]=n(r[a]),s.prototype["set"+e(r[a])]=function(e){return function(n){this[e]=Boolean(n)}}(r[a]);for(var u=0;u<t.length;u++)s.prototype["get"+e(t[u])]=n(t[u]),s.prototype["set"+e(t[u])]=function(e){return function(n){if(r=n,isNaN(parseFloat(r))||!isFinite(r))throw new TypeError(e+" must be a Number");var r;this[e]=Number(n)}}(t[u]);for(var l=0;l<o.length;l++)s.prototype["get"+e(o[l])]=n(o[l]),s.prototype["set"+e(o[l])]=function(e){return function(n){this[e]=String(n)}}(o[l]);return s});var SourceMap=function(e){var n={};function r(t){if(n[t])return n[t].exports;var o=n[t]={i:t,l:!1,exports:{}};return e[t].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=e,r.c=n,r.d=function(e,n,t){r.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:t})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,n){if(1&n&&(e=r(e)),8&n)return e;if(4&n&&"object"==typeof e&&e&&e.__esModule)return e;var t=Object.create(null);if(r.r(t),Object.defineProperty(t,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var o in e)r.d(t,o,function(n){return e[n]}.bind(null,o));return t},r.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(n,"a",n),n},r.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},r.p="",r(r.s=1)}([function(e,n){n.getArg=function(e,n,r){if(n in e)return e[n];if(3===arguments.length)return r;throw new Error('"'+n+'" is a required argument.')};var r=/^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.]*)(?::(\d+))?(\S*)$/,t=/^data:.+\,.+$/;function o(e){var n=e.match(r);return n?{scheme:n[1],auth:n[2],host:n[3],port:n[4],path:n[5]}:null}function i(e){var n="";return e.scheme&&(n+=e.scheme+":"),n+="//",e.auth&&(n+=e.auth+"@"),e.host&&(n+=e.host),e.port&&(n+=":"+e.port),e.path&&(n+=e.path),n}function s(e){var r=e,t=o(e);if(t){if(!t.path)return e;r=t.path}for(var s,a=n.isAbsolute(r),u=r.split(/\/+/),l=0,c=u.length-1;c>=0;c--)"."===(s=u[c])?u.splice(c,1):".."===s?l++:l>0&&(""===s?(u.splice(c+1,l),l=0):(u.splice(c,2),l--));return""===(r=u.join("/"))&&(r=a?"/":"."),t?(t.path=r,i(t)):r}n.urlParse=o,n.urlGenerate=i,n.normalize=s,n.join=function(e,n){""===e&&(e="."),""===n&&(n=".");var r=o(n),a=o(e);if(a&&(e=a.path||"/"),r&&!r.scheme)return a&&(r.scheme=a.scheme),i(r);if(r||n.match(t))return n;if(a&&!a.host&&!a.path)return a.host=n,i(a);var u="/"===n.charAt(0)?n:s(e.replace(/\/+$/,"")+"/"+n);return a?(a.path=u,i(a)):u},n.isAbsolute=function(e){return"/"===e.charAt(0)||!!e.match(r)},n.relative=function(e,n){""===e&&(e="."),e=e.replace(/\/$/,"");for(var r=0;0!==n.indexOf(e+"/");){var t=e.lastIndexOf("/");if(t<0)return n;if((e=e.slice(0,t)).match(/^([^\/]+:\/)?\/*$/))return n;++r}return Array(r+1).join("../")+n.substr(e.length+1)};var a=!("__proto__"in Object.create(null));function u(e){return e}function l(e){if(!e)return!1;var n=e.length;if(n<9)return!1;if(95!==e.charCodeAt(n-1)||95!==e.charCodeAt(n-2)||111!==e.charCodeAt(n-3)||116!==e.charCodeAt(n-4)||111!==e.charCodeAt(n-5)||114!==e.charCodeAt(n-6)||112!==e.charCodeAt(n-7)||95!==e.charCodeAt(n-8)||95!==e.charCodeAt(n-9))return!1;for(var r=n-10;r>=0;r--)if(36!==e.charCodeAt(r))return!1;return!0}function c(e,n){return e===n?0:e>n?1:-1}n.toSetString=a?u:function(e){return l(e)?"$"+e:e},n.fromSetString=a?u:function(e){return l(e)?e.slice(1):e},n.compareByOriginalPositions=function(e,n,r){var t=e.source-n.source;return 0!==t?t:0!=(t=e.originalLine-n.originalLine)?t:0!=(t=e.originalColumn-n.originalColumn)||r?t:0!=(t=e.generatedColumn-n.generatedColumn)?t:0!=(t=e.generatedLine-n.generatedLine)?t:e.name-n.name},n.compareByGeneratedPositionsDeflated=function(e,n,r){var t=e.generatedLine-n.generatedLine;return 0!==t?t:0!=(t=e.generatedColumn-n.generatedColumn)||r?t:0!=(t=e.source-n.source)?t:0!=(t=e.originalLine-n.originalLine)?t:0!=(t=e.originalColumn-n.originalColumn)?t:e.name-n.name},n.compareByGeneratedPositionsInflated=function(e,n){var r=e.generatedLine-n.generatedLine;return 0!==r?r:0!=(r=e.generatedColumn-n.generatedColumn)?r:0!==(r=c(e.source,n.source))?r:0!=(r=e.originalLine-n.originalLine)?r:0!=(r=e.originalColumn-n.originalColumn)?r:c(e.name,n.name)}},function(e,n,r){var t=r(0),o=r(2),i=r(3).ArraySet,s=r(4),a=r(6).quickSort;function u(e){var n=e;return"string"==typeof e&&(n=JSON.parse(e.replace(/^\)\]\}'/,""))),null!=n.sections?new g(n):new l(n)}function l(e){var n=e;"string"==typeof e&&(n=JSON.parse(e.replace(/^\)\]\}'/,"")));var r=t.getArg(n,"version"),o=t.getArg(n,"sources"),s=t.getArg(n,"names",[]),a=t.getArg(n,"sourceRoot",null),u=t.getArg(n,"sourcesContent",null),l=t.getArg(n,"mappings"),c=t.getArg(n,"file",null);if(r!=this._version)throw new Error("Unsupported version: "+r);o=o.map(String).map(t.normalize).map(function(e){return a&&t.isAbsolute(a)&&t.isAbsolute(e)?t.relative(a,e):e}),this._names=i.fromArray(s.map(String),!0),this._sources=i.fromArray(o,!0),this.sourceRoot=a,this.sourcesContent=u,this._mappings=l,this.file=c}function c(){this.generatedLine=0,this.generatedColumn=0,this.source=null,this.originalLine=null,this.originalColumn=null,this.name=null}function g(e){var n=e;"string"==typeof e&&(n=JSON.parse(e.replace(/^\)\]\}'/,"")));var r=t.getArg(n,"version"),o=t.getArg(n,"sections");if(r!=this._version)throw new Error("Unsupported version: "+r);this._sources=new i,this._names=new i;var s={line:-1,column:0};this._sections=o.map(function(e){if(e.url)throw new Error("Support for url field in sections not implemented.");var n=t.getArg(e,"offset"),r=t.getArg(n,"line"),o=t.getArg(n,"column");if(r<s.line||r===s.line&&o<s.column)throw new Error("Section offsets must be ordered and non-overlapping.");return s=n,{generatedOffset:{generatedLine:r+1,generatedColumn:o+1},consumer:new u(t.getArg(e,"map"))}})}u.fromSourceMap=function(e){return l.fromSourceMap(e)},u.prototype._version=3,u.prototype.__generatedMappings=null,Object.defineProperty(u.prototype,"_generatedMappings",{get:function(){return this.__generatedMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__generatedMappings}}),u.prototype.__originalMappings=null,Object.defineProperty(u.prototype,"_originalMappings",{get:function(){return this.__originalMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__originalMappings}}),u.prototype._charIsMappingSeparator=function(e,n){var r=e.charAt(n);return";"===r||","===r},u.prototype._parseMappings=function(e,n){throw new Error("Subclasses must implement _parseMappings")},u.GENERATED_ORDER=1,u.ORIGINAL_ORDER=2,u.GREATEST_LOWER_BOUND=1,u.LEAST_UPPER_BOUND=2,u.prototype.eachMapping=function(e,n,r){var o,i=n||null;switch(r||u.GENERATED_ORDER){case u.GENERATED_ORDER:o=this._generatedMappings;break;case u.ORIGINAL_ORDER:o=this._originalMappings;break;default:throw new Error("Unknown order of iteration.")}var s=this.sourceRoot;o.map(function(e){var n=null===e.source?null:this._sources.at(e.source);return null!=n&&null!=s&&(n=t.join(s,n)),{source:n,generatedLine:e.generatedLine,generatedColumn:e.generatedColumn,originalLine:e.originalLine,originalColumn:e.originalColumn,name:null===e.name?null:this._names.at(e.name)}},this).forEach(e,i)},u.prototype.allGeneratedPositionsFor=function(e){var n=t.getArg(e,"line"),r={source:t.getArg(e,"source"),originalLine:n,originalColumn:t.getArg(e,"column",0)};if(null!=this.sourceRoot&&(r.source=t.relative(this.sourceRoot,r.source)),!this._sources.has(r.source))return[];r.source=this._sources.indexOf(r.source);var i=[],s=this._findMapping(r,this._originalMappings,"originalLine","originalColumn",t.compareByOriginalPositions,o.LEAST_UPPER_BOUND);if(s>=0){var a=this._originalMappings[s];if(void 0===e.column)for(var u=a.originalLine;a&&a.originalLine===u;)i.push({line:t.getArg(a,"generatedLine",null),column:t.getArg(a,"generatedColumn",null),lastColumn:t.getArg(a,"lastGeneratedColumn",null)}),a=this._originalMappings[++s];else for(var l=a.originalColumn;a&&a.originalLine===n&&a.originalColumn==l;)i.push({line:t.getArg(a,"generatedLine",null),column:t.getArg(a,"generatedColumn",null),lastColumn:t.getArg(a,"lastGeneratedColumn",null)}),a=this._originalMappings[++s]}return i},n.SourceMapConsumer=u,l.prototype=Object.create(u.prototype),l.prototype.consumer=u,l.fromSourceMap=function(e){var n=Object.create(l.prototype),r=n._names=i.fromArray(e._names.toArray(),!0),o=n._sources=i.fromArray(e._sources.toArray(),!0);n.sourceRoot=e._sourceRoot,n.sourcesContent=e._generateSourcesContent(n._sources.toArray(),n.sourceRoot),n.file=e._file;for(var s=e._mappings.toArray().slice(),u=n.__generatedMappings=[],g=n.__originalMappings=[],f=0,p=s.length;f<p;f++){var h=s[f],m=new c;m.generatedLine=h.generatedLine,m.generatedColumn=h.generatedColumn,h.source&&(m.source=o.indexOf(h.source),m.originalLine=h.originalLine,m.originalColumn=h.originalColumn,h.name&&(m.name=r.indexOf(h.name)),g.push(m)),u.push(m)}return a(n.__originalMappings,t.compareByOriginalPositions),n},l.prototype._version=3,Object.defineProperty(l.prototype,"sources",{get:function(){return this._sources.toArray().map(function(e){return null!=this.sourceRoot?t.join(this.sourceRoot,e):e},this)}}),l.prototype._parseMappings=function(e,n){for(var r,o,i,u,l,g=1,f=0,p=0,h=0,m=0,d=0,_=e.length,v=0,y={},b={},C=[],w=[];v<_;)if(";"===e.charAt(v))g++,v++,f=0;else if(","===e.charAt(v))v++;else{for((r=new c).generatedLine=g,u=v;u<_&&!this._charIsMappingSeparator(e,u);u++);if(i=y[o=e.slice(v,u)])v+=o.length;else{for(i=[];v<u;)s.decode(e,v,b),l=b.value,v=b.rest,i.push(l);if(2===i.length)throw new Error("Found a source, but no line and column");if(3===i.length)throw new Error("Found a source and line, but no column");y[o]=i}r.generatedColumn=f+i[0],f=r.generatedColumn,i.length>1&&(r.source=m+i[1],m+=i[1],r.originalLine=p+i[2],p=r.originalLine,r.originalLine+=1,r.originalColumn=h+i[3],h=r.originalColumn,i.length>4&&(r.name=d+i[4],d+=i[4])),w.push(r),"number"==typeof r.originalLine&&C.push(r)}a(w,t.compareByGeneratedPositionsDeflated),this.__generatedMappings=w,a(C,t.compareByOriginalPositions),this.__originalMappings=C},l.prototype._findMapping=function(e,n,r,t,i,s){if(e[r]<=0)throw new TypeError("Line must be greater than or equal to 1, got "+e[r]);if(e[t]<0)throw new TypeError("Column must be greater than or equal to 0, got "+e[t]);return o.search(e,n,i,s)},l.prototype.computeColumnSpans=function(){for(var e=0;e<this._generatedMappings.length;++e){var n=this._generatedMappings[e];if(e+1<this._generatedMappings.length){var r=this._generatedMappings[e+1];if(n.generatedLine===r.generatedLine){n.lastGeneratedColumn=r.generatedColumn-1;continue}}n.lastGeneratedColumn=1/0}},l.prototype.originalPositionFor=function(e){var n={generatedLine:t.getArg(e,"line"),generatedColumn:t.getArg(e,"column")},r=this._findMapping(n,this._generatedMappings,"generatedLine","generatedColumn",t.compareByGeneratedPositionsDeflated,t.getArg(e,"bias",u.GREATEST_LOWER_BOUND));if(r>=0){var o=this._generatedMappings[r];if(o.generatedLine===n.generatedLine){var i=t.getArg(o,"source",null);null!==i&&(i=this._sources.at(i),null!=this.sourceRoot&&(i=t.join(this.sourceRoot,i)));var s=t.getArg(o,"name",null);return null!==s&&(s=this._names.at(s)),{source:i,line:t.getArg(o,"originalLine",null),column:t.getArg(o,"originalColumn",null),name:s}}}return{source:null,line:null,column:null,name:null}},l.prototype.hasContentsOfAllSources=function(){return!!this.sourcesContent&&this.sourcesContent.length>=this._sources.size()&&!this.sourcesContent.some(function(e){return null==e})},l.prototype.sourceContentFor=function(e,n){if(!this.sourcesContent)return null;if(null!=this.sourceRoot&&(e=t.relative(this.sourceRoot,e)),this._sources.has(e))return this.sourcesContent[this._sources.indexOf(e)];var r;if(null!=this.sourceRoot&&(r=t.urlParse(this.sourceRoot))){var o=e.replace(/^file:\/\//,"");if("file"==r.scheme&&this._sources.has(o))return this.sourcesContent[this._sources.indexOf(o)];if((!r.path||"/"==r.path)&&this._sources.has("/"+e))return this.sourcesContent[this._sources.indexOf("/"+e)]}if(n)return null;throw new Error('"'+e+'" is not in the SourceMap.')},l.prototype.generatedPositionFor=function(e){var n=t.getArg(e,"source");if(null!=this.sourceRoot&&(n=t.relative(this.sourceRoot,n)),!this._sources.has(n))return{line:null,column:null,lastColumn:null};var r={source:n=this._sources.indexOf(n),originalLine:t.getArg(e,"line"),originalColumn:t.getArg(e,"column")},o=this._findMapping(r,this._originalMappings,"originalLine","originalColumn",t.compareByOriginalPositions,t.getArg(e,"bias",u.GREATEST_LOWER_BOUND));if(o>=0){var i=this._originalMappings[o];if(i.source===r.source)return{line:t.getArg(i,"generatedLine",null),column:t.getArg(i,"generatedColumn",null),lastColumn:t.getArg(i,"lastGeneratedColumn",null)}}return{line:null,column:null,lastColumn:null}},n.BasicSourceMapConsumer=l,g.prototype=Object.create(u.prototype),g.prototype.constructor=u,g.prototype._version=3,Object.defineProperty(g.prototype,"sources",{get:function(){for(var e=[],n=0;n<this._sections.length;n++)for(var r=0;r<this._sections[n].consumer.sources.length;r++)e.push(this._sections[n].consumer.sources[r]);return e}}),g.prototype.originalPositionFor=function(e){var n={generatedLine:t.getArg(e,"line"),generatedColumn:t.getArg(e,"column")},r=o.search(n,this._sections,function(e,n){return e.generatedLine-n.generatedOffset.generatedLine||e.generatedColumn-n.generatedOffset.generatedColumn}),i=this._sections[r];return i?i.consumer.originalPositionFor({line:n.generatedLine-(i.generatedOffset.generatedLine-1),column:n.generatedColumn-(i.generatedOffset.generatedLine===n.generatedLine?i.generatedOffset.generatedColumn-1:0),bias:e.bias}):{source:null,line:null,column:null,name:null}},g.prototype.hasContentsOfAllSources=function(){return this._sections.every(function(e){return e.consumer.hasContentsOfAllSources()})},g.prototype.sourceContentFor=function(e,n){for(var r=0;r<this._sections.length;r++){var t=this._sections[r].consumer.sourceContentFor(e,!0);if(t)return t}if(n)return null;throw new Error('"'+e+'" is not in the SourceMap.')},g.prototype.generatedPositionFor=function(e){for(var n=0;n<this._sections.length;n++){var r=this._sections[n];if(-1!==r.consumer.sources.indexOf(t.getArg(e,"source"))){var o=r.consumer.generatedPositionFor(e);if(o)return{line:o.line+(r.generatedOffset.generatedLine-1),column:o.column+(r.generatedOffset.generatedLine===o.line?r.generatedOffset.generatedColumn-1:0)}}}return{line:null,column:null}},g.prototype._parseMappings=function(e,n){this.__generatedMappings=[],this.__originalMappings=[];for(var r=0;r<this._sections.length;r++)for(var o=this._sections[r],i=o.consumer._generatedMappings,s=0;s<i.length;s++){var u=i[s],l=o.consumer._sources.at(u.source);null!==o.consumer.sourceRoot&&(l=t.join(o.consumer.sourceRoot,l)),this._sources.add(l),l=this._sources.indexOf(l);var c=o.consumer._names.at(u.name);this._names.add(c),c=this._names.indexOf(c);var g={source:l,generatedLine:u.generatedLine+(o.generatedOffset.generatedLine-1),generatedColumn:u.generatedColumn+(o.generatedOffset.generatedLine===u.generatedLine?o.generatedOffset.generatedColumn-1:0),originalLine:u.originalLine,originalColumn:u.originalColumn,name:c};this.__generatedMappings.push(g),"number"==typeof g.originalLine&&this.__originalMappings.push(g)}a(this.__generatedMappings,t.compareByGeneratedPositionsDeflated),a(this.__originalMappings,t.compareByOriginalPositions)},n.IndexedSourceMapConsumer=g},function(e,n){n.GREATEST_LOWER_BOUND=1,n.LEAST_UPPER_BOUND=2,n.search=function(e,r,t,o){if(0===r.length)return-1;var i=function e(r,t,o,i,s,a){var u=Math.floor((t-r)/2)+r,l=s(o,i[u],!0);return 0===l?u:l>0?t-u>1?e(u,t,o,i,s,a):a==n.LEAST_UPPER_BOUND?t<i.length?t:-1:u:u-r>1?e(r,u,o,i,s,a):a==n.LEAST_UPPER_BOUND?u:r<0?-1:r}(-1,r.length,e,r,t,o||n.GREATEST_LOWER_BOUND);if(i<0)return-1;for(;i-1>=0&&0===t(r[i],r[i-1],!0);)--i;return i}},function(e,n,r){var t=r(0),o=Object.prototype.hasOwnProperty;function i(){this._array=[],this._set=Object.create(null)}i.fromArray=function(e,n){for(var r=new i,t=0,o=e.length;t<o;t++)r.add(e[t],n);return r},i.prototype.size=function(){return Object.getOwnPropertyNames(this._set).length},i.prototype.add=function(e,n){var r=t.toSetString(e),i=o.call(this._set,r),s=this._array.length;i&&!n||this._array.push(e),i||(this._set[r]=s)},i.prototype.has=function(e){var n=t.toSetString(e);return o.call(this._set,n)},i.prototype.indexOf=function(e){var n=t.toSetString(e);if(o.call(this._set,n))return this._set[n];throw new Error('"'+e+'" is not in the set.')},i.prototype.at=function(e){if(e>=0&&e<this._array.length)return this._array[e];throw new Error("No element indexed by "+e)},i.prototype.toArray=function(){return this._array.slice()},n.ArraySet=i},function(e,n,r){var t=r(5);n.encode=function(e){var n,r="",o=function(e){return e<0?1+(-e<<1):0+(e<<1)}(e);do{n=31&o,(o>>>=5)>0&&(n|=32),r+=t.encode(n)}while(o>0);return r},n.decode=function(e,n,r){var o,i,s,a,u=e.length,l=0,c=0;do{if(n>=u)throw new Error("Expected more digits in base 64 VLQ value.");if(-1===(i=t.decode(e.charCodeAt(n++))))throw new Error("Invalid base64 digit: "+e.charAt(n-1));o=!!(32&i),l+=(i&=31)<<c,c+=5}while(o);r.value=(a=(s=l)>>1,1==(1&s)?-a:a),r.rest=n}},function(e,n){var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");n.encode=function(e){if(0<=e&&e<r.length)return r[e];throw new TypeError("Must be between 0 and 63: "+e)},n.decode=function(e){return 65<=e&&e<=90?e-65:97<=e&&e<=122?e-97+26:48<=e&&e<=57?e-48+52:43==e?62:47==e?63:-1}},function(e,n){function r(e,n,r){var t=e[n];e[n]=e[r],e[r]=t}n.quickSort=function(e,n){!function e(n,t,o,i){if(o<i){var s=o-1;r(n,(c=o,g=i,Math.round(c+Math.random()*(g-c))),i);for(var a=n[i],u=o;u<i;u++)t(n[u],a)<=0&&r(n,s+=1,u);r(n,s+1,u);var l=s+1;e(n,t,o,l-1),e(n,t,l+1,i)}var c,g}(e,n,0,e.length-1)}}]);!function(e,n){"use strict";"function"==typeof define&&define.amd?define("stacktrace-gps",["source-map","stackframe"],n):"object"==typeof exports?module.exports=n(require("source-map/lib/source-map-consumer"),require("stackframe")):e.StackTraceGPS=n(e.SourceMap||e.sourceMap,e.StackFrame)}(this,function(e,n){"use strict";function r(e){return new Promise(function(n,r){var t=new XMLHttpRequest;t.open("get",e),t.onerror=r,t.onreadystatechange=function(){4===t.readyState&&(t.status>=200&&t.status<300||"file://"===e.substr(0,7)&&t.responseText?n(t.responseText):r(new Error("HTTP status: "+t.status+" retrieving "+e)))},t.send()})}function t(e){if("undefined"!=typeof window&&window.atob)return window.atob(e);throw new Error("You must supply a polyfill for window.atob in this environment")}function o(e){if("object"!=typeof e)throw new TypeError("Given StackFrame is not an object");if("string"!=typeof e.fileName)throw new TypeError("Given file name is not a String");if("number"!=typeof e.lineNumber||e.lineNumber%1!=0||e.lineNumber<1)throw new TypeError("Given line number must be a positive integer");if("number"!=typeof e.columnNumber||e.columnNumber%1!=0||e.columnNumber<0)throw new TypeError("Given column number must be a non-negative integer");return!0}return function i(s){if(!(this instanceof i))return new i(s);s=s||{},this.sourceCache=s.sourceCache||{},this.sourceMapConsumerCache=s.sourceMapConsumerCache||{},this.ajax=s.ajax||r,this._atob=s.atob||t,this._get=function(e){return new Promise(function(n,r){var t="data:"===e.substr(0,5);if(this.sourceCache[e])n(this.sourceCache[e]);else if(s.offline&&!t)r(new Error("Cannot make network requests in offline mode"));else if(t){var o=e.match(/^data:application\/json;([\w=:"-]+;)*base64,/);if(o){var i=o[0].length,a=e.substr(i),u=this._atob(a);this.sourceCache[e]=u,n(u)}else r(new Error("The encoding of the inline sourcemap is not supported"))}else{var l=this.ajax(e,{method:"get"});this.sourceCache[e]=l,l.then(n,r)}}.bind(this))},this._getSourceMapConsumer=function(n,r){return new Promise(function(t){if(this.sourceMapConsumerCache[n])t(this.sourceMapConsumerCache[n]);else{var o=new Promise(function(t,o){return this._get(n).then(function(n){"string"==typeof n&&(n=function(e){if("undefined"!=typeof JSON&&JSON.parse)return JSON.parse(e);throw new Error("You must supply a polyfill for JSON.parse in this environment")}(n.replace(/^\)\]\}'/,""))),void 0===n.sourceRoot&&(n.sourceRoot=r),t(new e.SourceMapConsumer(n))}).catch(o)}.bind(this));this.sourceMapConsumerCache[n]=o,t(o)}}.bind(this))},this.pinpoint=function(e){return new Promise(function(n,r){this.getMappedLocation(e).then(function(e){function r(){n(e)}this.findFunctionName(e).then(n,r).catch(r)}.bind(this),r)}.bind(this))},this.findFunctionName=function(e){return new Promise(function(r,t){o(e),this._get(e.fileName).then(function(t){var o=e.lineNumber,i=e.columnNumber,s=function(e,n){for(var r=[/['"]?([$_A-Za-z][$_A-Za-z0-9]*)['"]?\s*[:=]\s*function\b/,/function\s+([^('"`]*?)\s*\(([^)]*)\)/,/['"]?([$_A-Za-z][$_A-Za-z0-9]*)['"]?\s*[:=]\s*(?:eval|new Function)\b/,/\b(?!(?:if|for|switch|while|with|catch)\b)(?:(?:static)\s+)?(\S+)\s*\(.*?\)\s*\{/,/['"]?([$_A-Za-z][$_A-Za-z0-9]*)['"]?\s*[:=]\s*\(.*?\)\s*=>/],t=e.split("\n"),o="",i=Math.min(n,20),s=0;s<i;++s){var a=t[n-s-1],u=a.indexOf("//");if(u>=0&&(a=a.substr(0,u)),a){o=a+o;for(var l=r.length,c=0;c<l;c++){var g=r[c].exec(o);if(g&&g[1])return g[1]}}}}(t,o);r(s?new n({functionName:s,args:e.args,fileName:e.fileName,lineNumber:o,columnNumber:i}):e)},t).catch(t)}.bind(this))},this.getMappedLocation=function(e){return new Promise(function(r,t){!function(){if("function"!=typeof Object.defineProperty||"function"!=typeof Object.create)throw new Error("Unable to consume source maps in older browsers")}(),o(e);var i=this.sourceCache,s=e.fileName;this._get(s).then(function(t){var o=function(e){for(var n,r,t=/\/\/[#@] ?sourceMappingURL=([^\s'"]+)\s*$/gm;r=t.exec(e);)n=r[1];if(n)return n;throw new Error("sourceMappingURL not found")}(t),a="data:"===o.substr(0,5),u=s.substring(0,s.lastIndexOf("/")+1);return"/"===o[0]||a||/^https?:\/\/|^\/\//i.test(o)||(o=u+o),this._getSourceMapConsumer(o,u).then(function(t){return function(e,r,t){return new Promise(function(o,i){var s=r.originalPositionFor({line:e.lineNumber,column:e.columnNumber});if(s.source){var a=r.sourceContentFor(s.source);a&&(t[s.source]=a),o(new n({functionName:s.name||e.functionName,args:e.args,fileName:s.source,lineNumber:s.line,columnNumber:s.column}))}else i(new Error("Could not get original source for given stackframe and source map"))})}(e,t,i).then(r).catch(function(){r(e)})})}.bind(this),t).catch(t)}.bind(this))}}});
//# sourceMappingURL=stacktrace-gps.min.js.map