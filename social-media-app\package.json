{"name": "social-media-app", "license": "0BSD", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.24.0", "@react-native-firebase/app": "^22.2.0", "@react-native-firebase/auth": "^22.2.0", "@react-native-firebase/crashlytics": "^22.2.0", "expo": "~53.0.9", "expo-dev-client": "~5.1.8", "expo-status-bar": "~2.2.3", "firebase": "^11.8.1", "react": "19.0.0", "react-native": "0.79.2"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}