// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { initializeAuth, getReactNativePersistence } from "firebase/auth";
import ReactNativeAsyncStorage from '@react-native-async-storage/async-storage';
//import { getAnalytics } from "firebase/analytics";
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyAViKZsh7jsWjUFWmapzCElIM101kjz5e4",
  authDomain: "social-media-d5baa.firebaseapp.com",
  projectId: "social-media-d5baa",
  storageBucket: "social-media-d5baa.firebasestorage.app",
  messagingSenderId: "48168767870",
  appId: "1:48168767870:web:59556159a576a3498d632f",
  measurementId: "G-Q9V3FCLFW0"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Auth with React Native persistence
const auth = initializeAuth(app, {
  persistence: getReactNativePersistence(ReactNativeAsyncStorage)
});

//const analytics = getAnalytics(app);

// Export the auth instance so it can be used in other files
export { auth };